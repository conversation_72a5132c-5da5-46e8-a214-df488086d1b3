import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import SplitText from '../components/reactbits/textanimations/SplitText';
import ShinyText from '../components/reactbits/textanimations/ShinyText';
import Aurora from '../components/reactbits/backgrounds/Aurora';

const OpeningScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  const subtitleFadeIn = interpolate(currentFrame, [30, 60], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="bg-black flex flex-col items-center justify-center text-white relative overflow-hidden">
      {/* Aurora 背景 */}
      <div className="absolute inset-0 opacity-60">
        <Aurora
          colorStops={["#5227FF", "#7cff67", "#ff6b6b", "#4ecdc4"]}
          amplitude={1.2}
          blend={0.7}
          speed={0.8}
        />
      </div>

      {/* 主标题区域 */}
      <div className="relative z-10 text-center max-w-4xl px-8">
        {/* 主标题 */}
        <SplitText
          text="早睡是伪命题吗？"
          className="text-2xl font-semibold text-center"
          delay={100}
          duration={0.6}
          ease="power3.out"
          splitType="chars"
          from={{ opacity: 0, y: 40 }}
          to={{ opacity: 1, y: 0 }}
          threshold={0.1}
          rootMargin="-100px"
          textAlign="center"
        />

        {/* 副标题 */}
        <div
          className="text-xl md:text-2xl mb-16 leading-relaxed"
          style={{ opacity: subtitleFadeIn }}
        >
          <div className="mb-4">
            <ShinyText
              text="很多人相信：只要早睡，就能变健康、变聪明"
              className="text-gray-300"
              speed={4}
            />
          </div>
          <div className="mb-4">
            <ShinyText
              text="甚至改变人生"
              className="text-gray-300"
              speed={4}
            />
          </div>
        </div>

      </div>
    </AbsoluteFill>
  );
};

export default OpeningScene;